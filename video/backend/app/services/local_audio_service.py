"""
Local audio processing service for speech recognition
Integrated from the standalone audio module
"""

import os
import json
import time
import threading
from typing import Dict, List, Optional, Any
from pathlib import Path
from datetime import datetime
from loguru import logger

try:
    import GPUtil
    import soundfile
    from modelscope.pipelines import pipeline
    from modelscope.utils.constant import Tasks
    from pydantic import BaseModel, Field, field_validator
    AUDIO_DEPENDENCIES_AVAILABLE = True
except ImportError as e:
    logger.warning(f"Audio processing dependencies not available: {e}")
    AUDIO_DEPENDENCIES_AVAILABLE = False
    # Provide fallback for BaseModel when pydantic is not available
    class BaseModel:
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)

    def Field(*args, **kwargs):
        return None

    def field_validator(*args, **kwargs):
        def decorator(func):
            return func
        return decorator


# Configuration Management
class AudioConfig:
    """Audio processing configuration management"""

    def __init__(self):
        self.enabled = os.getenv("ENABLE_PARSE_AUDIO", "true").lower() == "true" and AUDIO_DEPENDENCIES_AVAILABLE
        self.gpu_device = self._get_optimal_gpu_device() if AUDIO_DEPENDENCIES_AVAILABLE else "cpu"
        self.batch_size = int(os.getenv("AUDIO_BATCH_SIZE", "10"))
        self.batch_size_s = int(os.getenv("AUDIO_BATCH_SIZE_S", "300"))
        self.batch_size_threshold_s = int(os.getenv("AUDIO_BATCH_SIZE_THRESHOLD_S", "60"))
        self.max_file_size = int(os.getenv("AUDIO_MAX_FILE_SIZE", "100")) * 1024 * 1024  # MB to bytes
        self.supported_formats = ['.wav', '.mp3', '.flac', '.m4a', '.aac', '.ogg', '.wma']
        self.temp_dir = os.getenv("AUDIO_TEMP_DIR", "/tmp/audio_processing")
        self.cache_enabled = os.getenv("AUDIO_CACHE_ENABLED", "true").lower() == "true"
        self.max_concurrent_tasks = int(os.getenv("AUDIO_MAX_CONCURRENT_TASKS", "3"))

        # Ensure temp directory exists
        os.makedirs(self.temp_dir, exist_ok=True)

    def _get_optimal_gpu_device(self) -> str:
        """Automatically select the best available GPU device"""
        try:
            gpus = GPUtil.getGPUs()
            if not gpus:
                logger.warning("No GPUs available, using CPU")
                return "cpu"

            # Select GPU with most free memory
            best_gpu = min(gpus, key=lambda gpu: gpu.memoryUsed)
            device = f"cuda:{best_gpu.id}"
            logger.info(f"Selected GPU device: {device} (Memory: {best_gpu.memoryFree}MB free)")
            return device
        except Exception as e:
            logger.warning(f"Failed to detect GPU: {e}, using CPU")
            return "cpu"

    def is_supported_format(self, filename: str) -> bool:
        """Check if file format is supported"""
        return Path(filename).suffix.lower() in self.supported_formats


# Data Models
class AudioMetadata(BaseModel):
    """Audio file metadata"""
    filename: str
    duration: Optional[float] = None
    sample_rate: Optional[int] = None
    channels: Optional[int] = None
    format: Optional[str] = None
    size_bytes: Optional[int] = None
    bitrate: Optional[int] = None


class RecognitionResponse(BaseModel):
    """Speech recognition response"""
    output: Any = Field(..., description="Recognition result")
    metadata: Optional[AudioMetadata] = None
    processing_time: Optional[float] = None
    model_info: Optional[Dict[str, str]] = None


# Audio Processing Utilities
class AudioValidator:
    """Audio file validation utilities"""

    @staticmethod
    def validate_file_format(file_path: str) -> bool:
        """Validate if file format is supported"""
        return config.is_supported_format(file_path)

    @staticmethod
    def validate_file_size(file_path: str) -> bool:
        """Validate if file size is within limits"""
        try:
            size = os.path.getsize(file_path)
            return size <= config.max_file_size
        except OSError:
            return False

    @staticmethod
    def get_audio_metadata(file_path: str) -> AudioMetadata:
        """Extract audio metadata"""
        try:
            if not AUDIO_DEPENDENCIES_AVAILABLE:
                return AudioMetadata(filename=os.path.basename(file_path))
                
            info = soundfile.info(file_path)
            file_size = os.path.getsize(file_path)

            return AudioMetadata(
                filename=os.path.basename(file_path),
                duration=info.duration,
                sample_rate=info.samplerate,
                channels=info.channels,
                format=info.format,
                size_bytes=file_size,
                bitrate=int(file_size * 8 / info.duration) if info.duration > 0 else None
            )
        except Exception as e:
            logger.error(f"Failed to extract metadata from {file_path}: {e}")
            return AudioMetadata(filename=os.path.basename(file_path))


# Model Management
class ModelManager:
    """Manage audio processing models"""

    def __init__(self):
        self._inference_pipeline = None
        self._pipeline_lock = threading.Lock()
        self._model_info = {
            "inference_model": "iic/speech_paraformer-large-vad-punc_asr_nat-zh-cn-16k-common-vocab8404-pytorch",
            "device": config.gpu_device,
            "loaded": False
        }

    def get_inference_pipeline(self):
        """Get or initialize inference pipeline"""
        if not AUDIO_DEPENDENCIES_AVAILABLE:
            raise Exception("Audio processing dependencies not available")
            
        if self._inference_pipeline is not None:
            return self._inference_pipeline

        with self._pipeline_lock:
            if self._inference_pipeline is not None:
                return self._inference_pipeline

            try:
                logger.info("Initializing inference pipeline...")
                self._inference_pipeline = pipeline(
                    task=Tasks.auto_speech_recognition,
                    model='iic/speech_paraformer-large-vad-punc_asr_nat-zh-cn-16k-common-vocab8404-pytorch',
                    vad_model='iic/speech_fsmn_vad_zh-cn-16k-common-pytorch',
                    vad_kwargs={"max_single_segment_time": 60000},
                    punc_model='iic/punc_ct-transformer_cn-en-common-vocab471067-large',
                    lm_model='iic/speech_transformer_lm_zh-cn-common-vocab8404-pytorch',
                    device=config.gpu_device,
                    batch_size=config.batch_size,
                    batch_size_s=config.batch_size_s,
                    batch_size_threshold_s=config.batch_size_threshold_s,
                    return_raw_text=True,
                    sentence_timestamp=True,
                    return_spk_res=False,
                    disable_update=True,
                )
                self._model_info["loaded"] = True
                logger.info("Inference pipeline initialized successfully")
                return self._inference_pipeline
            except Exception as e:
                logger.error(f"Failed to initialize inference pipeline: {e}")
                raise Exception(f"Model initialization failed: {str(e)}")

    def get_model_info(self) -> Dict[str, Any]:
        """Get model information"""
        return self._model_info.copy()

    def is_loaded(self) -> bool:
        """Check if models are loaded"""
        return self._model_info["loaded"]


# Initialize configuration and model manager
config = AudioConfig()
model_manager = ModelManager()

# Check if audio processing is enabled
if not config.enabled:
    if not AUDIO_DEPENDENCIES_AVAILABLE:
        logger.warning("Audio module is disabled due to missing dependencies")
    else:
        logger.warning("Audio parsing is disabled. Set ENABLE_PARSE_AUDIO=true to enable it.")
else:
    logger.info(f"Audio module initialized with device: {config.gpu_device}")


# Audio Processing Service
class LocalAudioService:
    """Local audio processing service for speech recognition"""

    def __init__(self):
        self.model_manager = model_manager
        self.validator = AudioValidator()

    def is_available(self) -> bool:
        """Check if audio processing is available"""
        print("Checking audio service availability...")
        print(config.enabled)
        print(AUDIO_DEPENDENCIES_AVAILABLE)
        return config.enabled and AUDIO_DEPENDENCIES_AVAILABLE

    async def process_audio(self, file_path: str, extract_metadata: bool = True) -> RecognitionResponse:
        """Process single audio file"""
        if not self.is_available():
            raise Exception("Audio processing service is not available")
            
        start_time = time.time()

        try:
            # Validate file
            if not self.validator.validate_file_format(file_path):
                raise Exception("Unsupported audio format")

            if not self.validator.validate_file_size(file_path):
                raise Exception("File size exceeds limit")

            # Get pipeline and process
            pipeline = self.model_manager.get_inference_pipeline()
            result = pipeline(file_path)

            # Extract metadata if requested
            metadata = None
            if extract_metadata:
                metadata = self.validator.get_audio_metadata(file_path)

            processing_time = time.time() - start_time

            return RecognitionResponse(
                output=result,
                metadata=metadata,
                processing_time=processing_time,
                model_info=self.model_manager.get_model_info()
            )

        except Exception as e:
            logger.error(f"Error processing audio {file_path}: {e}")
            raise Exception(f"Audio processing failed: {str(e)}")

    def generate_subtitle_from_audio(self, audio_file_path: str) -> List[Dict]:
        """
        Generate subtitle from audio file
        """
        try:
            if not self.is_available():
                raise Exception("Audio processing service is not available")

            # Check if audio file exists
            if not os.path.exists(audio_file_path):
                raise FileNotFoundError(f"Audio file not found: {audio_file_path}")

            logger.info(f"Processing audio file for subtitle generation: {audio_file_path}")

            # Process audio using local service
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                response = loop.run_until_complete(self.process_audio(audio_file_path, extract_metadata=True))
            finally:
                loop.close()

            # Extract recognition result
            recognition_result = response.output

            if not recognition_result:
                logger.warning("No recognition result from audio processing")
                return []

            # Convert recognition result to subtitle format
            subtitle_entries = []

            # Handle different result formats
            if isinstance(recognition_result, dict):
                if 'sentences' in recognition_result:
                    # Format with sentences and timestamps
                    sentences = recognition_result['sentences']
                    for i, sentence in enumerate(sentences):
                        if isinstance(sentence, dict) and 'text' in sentence:
                            start_time = sentence.get('start', i * 2.0)  # Default 2s per sentence
                            end_time = sentence.get('end', start_time + 2.0)
                            text = sentence['text'].strip()
                            
                            if text:
                                subtitle_entries.append({
                                    "index": i + 1,
                                    "start_time": start_time,
                                    "end_time": end_time,
                                    "text": text,
                                    "confidence": sentence.get('confidence', 0.9)
                                })
                elif 'text' in recognition_result:
                    # Simple text result - split into sentences
                    text = recognition_result['text']
                    sentences = self._split_text_into_sentences(text)
                    duration_per_sentence = 3.0  # Default 3 seconds per sentence
                    
                    for i, sentence in enumerate(sentences):
                        if sentence.strip():
                            start_time = i * duration_per_sentence
                            end_time = start_time + duration_per_sentence
                            
                            subtitle_entries.append({
                                "index": i + 1,
                                "start_time": start_time,
                                "end_time": end_time,
                                "text": sentence.strip(),
                                "confidence": 0.8
                            })
            elif isinstance(recognition_result, str):
                # Plain text result
                sentences = self._split_text_into_sentences(recognition_result)
                duration_per_sentence = 3.0
                
                for i, sentence in enumerate(sentences):
                    if sentence.strip():
                        start_time = i * duration_per_sentence
                        end_time = start_time + duration_per_sentence
                        
                        subtitle_entries.append({
                            "index": i + 1,
                            "start_time": start_time,
                            "end_time": end_time,
                            "text": sentence.strip(),
                            "confidence": 0.8
                        })

            logger.info(f"Generated {len(subtitle_entries)} subtitle entries from audio")
            return subtitle_entries

        except Exception as e:
            logger.error(f"Failed to generate subtitle from audio: {e}")
            raise Exception(f"Subtitle generation failed: {str(e)}")

    def _split_text_into_sentences(self, text: str) -> List[str]:
        """Split text into sentences"""
        import re
        # Split by Chinese and English sentence endings
        sentences = re.split(r'[。！？.!?]+', text)
        return [s.strip() for s in sentences if s.strip()]


# Global service instance
local_audio_service = LocalAudioService()
